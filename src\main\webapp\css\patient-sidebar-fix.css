/* 
 * Patient Sidebar Fix
 * This CSS file specifically fixes the sidebar in the patient interface
 * to ensure it doesn't overlap with the main content
 */

/* Fix for the sidebar navigation items */
.sidebar-menu li a {
    display: flex !important;
    align-items: center !important;
    padding: 12px 20px !important;
    color: #555 !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
    font-size: 14px !important;
    border-left: 3px solid transparent !important;
    width: 100% !important;
    box-sizing: border-box !important;
}

.sidebar-menu li a i {
    margin-right: 10px !important;
    width: 20px !important;
    text-align: center !important;
    font-size: 16px !important;
}

.sidebar-menu li a:hover {
    background-color: #f5f5f5 !important;
    color: #4CAF50 !important;
}

.sidebar-menu li.active a {
    background-color: #f5f5f5 !important;
    color: #4CAF50 !important;
    border-left: 3px solid #4CAF50 !important;
    font-weight: 500 !important;
}

/* Ensure the My Appointments and My Patients links are properly styled */
.sidebar-menu li a[href*="appointments.jsp"],
.sidebar-menu li a[href*="patients.jsp"] {
    position: relative !important;
    z-index: 10 !important;
    width: 100% !important;
    display: flex !important;
    align-items: center !important;
}

/* Fix for the main content area to prevent overlap with sidebar */
.patient-dashboard .main-content,
.patient-appointments .main-content,
.patient-profile .main-content {
    margin-left: 240px !important;
    width: calc(100% - 240px) !important;
    padding: 25px !important;
    box-sizing: border-box !important;
    transition: all 0.3s ease !important;
}

/* Fix for the sidebar */
.patient-dashboard .sidebar,
.patient-appointments .sidebar,
.patient-profile .sidebar {
    width: 240px !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    height: 100vh !important;
    overflow-y: auto !important;
    z-index: 1000 !important;
    background-color: #fff !important;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1) !important;
}

/* Ensure the sidebar doesn't overlap with content on mobile */
@media (max-width: 768px) {
    .patient-dashboard .main-content,
    .patient-appointments .main-content,
    .patient-profile .main-content {
        margin-left: 0 !important;
        width: 100% !important;
        padding: 15px !important;
    }
    
    .patient-dashboard .sidebar,
    .patient-appointments .sidebar,
    .patient-profile .sidebar {
        width: 100% !important;
        height: auto !important;
        position: relative !important;
    }
    
    .sidebar-menu {
        width: 100% !important;
        padding: 0 !important;
    }
    
    .sidebar-menu li {
        width: 100% !important;
    }
    
    .sidebar-menu li a {
        width: 100% !important;
        padding: 12px 15px !important;
    }
}
