/* MedDoc Dashboard CSS */
:root {
    --primary-color: #3CCFCF;
    --primary-dark: #2AACAC;
    --primary-light: #B3EDED;
    --secondary-color: #2ecc71;
    --secondary-dark: #27ae60;
    --secondary-light: #a9f5bc;
    --accent-color: #e74c3c;
    --dark-color: #2c3e50;
    --light-color: #f9f9f9;
    --danger-color: #e74c3c;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --info-color: #3498db;
    --text-dark: #2c3e50;
    --text-light: #7f8c8d;
    --border-color: #e0e0e0;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --white: #ffffff;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', 'Segoe UI', sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: var(--text-dark);
    background-color: var(--gray-100);
}

a {
    text-decoration: none;
    color: inherit;
}

ul {
    list-style: none;
}

/* Layout */
.dashboard-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.sidebar {
    width: 250px;
    background-color: var(--white);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
    z-index: 100;
}

.sidebar-collapsed {
    width: 70px;
}

.sidebar-header {
    padding: 20px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
    position: relative;
}

.sidebar-header img {
    height: 30px;
    margin-right: 10px;
}

.sidebar-header h2 {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-color);
}

.sidebar-collapsed .sidebar-header h2 {
    display: none;
}

.sidebar-toggle {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    font-size: 16px;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.sidebar-toggle:hover {
    background-color: var(--gray-200);
    color: var(--primary-color);
}

.profile-overview {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
}

.profile-overview h3 {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-light);
    display: flex;
    align-items: center;
}

.profile-overview h3 i {
    margin-right: 10px;
    color: var(--primary-color);
}

.sidebar-collapsed .profile-overview h3 span {
    display: none;
}

.sidebar-menu {
    flex: 1;
    padding: 10px 0;
}

.sidebar-menu ul li {
    margin-bottom: 5px;
}

.sidebar-menu ul li a {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: var(--text-dark);
    transition: all 0.3s ease;
}

.sidebar-menu ul li a:hover {
    background-color: var(--primary-light);
    color: var(--primary-dark);
}

.sidebar-menu ul li.active a {
    background-color: var(--primary-color);
    color: var(--white);
}

.sidebar-menu ul li a i {
    margin-right: 10px;
    font-size: 16px;
}

.sidebar-collapsed .sidebar-menu ul li a span {
    display: none;
}

.sidebar-menu ul li.logout {
    margin-top: auto;
}

.sidebar-menu ul li.logout a {
    color: var(--danger-color);
}

.sidebar-menu ul li.logout a:hover {
    background-color: var(--danger-color);
    color: var(--white);
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow-x: hidden;
}

/* Header */
.top-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 30px;
    background-color: var(--white);
    border-bottom: 1px solid var(--border-color);
    box-shadow: 0 2px 4px var(--shadow-color);
}

.logo-container {
    display: flex;
    align-items: center;
}

.logo-container img {
    height: 30px;
    margin-right: 10px;
}

.logo-container h1 {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-color);
}

.header-nav {
    display: flex;
    align-items: center;
}

.header-nav a {
    padding: 8px 15px;
    margin-right: 5px;
    color: var(--text-dark);
    border-radius: 4px;
    transition: all 0.3s ease;
}

.header-nav a:hover {
    background-color: var(--gray-200);
}

.header-nav a.active {
    color: var(--primary-color);
    font-weight: 500;
}

.header-actions {
    display: flex;
    align-items: center;
}

.search-container {
    position: relative;
    margin-right: 15px;
}

.search-input {
    padding: 8px 15px 8px 35px;
    border: 1px solid var(--border-color);
    border-radius: 20px;
    background-color: var(--gray-100);
    width: 200px;
    transition: all 0.3s ease;
}

.search-input:focus {
    width: 250px;
    outline: none;
    border-color: var(--primary-color);
}

.search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
}

.user-profile {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.user-profile:hover {
    background-color: var(--gray-200);
}

.user-profile img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 10px;
}

.user-profile .user-info {
    display: flex;
    flex-direction: column;
}

.user-profile .user-name {
    font-weight: 500;
    font-size: 14px;
}

.user-profile .user-role {
    font-size: 12px;
    color: var(--text-light);
}

.user-profile .dropdown-icon {
    margin-left: 10px;
    color: var(--text-light);
}

/* Content Area */
.content-area {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
}

/* Tabs */
.tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 20px;
}

.tab {
    padding: 10px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 2px solid transparent;
}

.tab.active {
    border-bottom-color: var(--primary-color);
    color: var(--primary-color);
    font-weight: 500;
}

/* Cards */
.card {
    background-color: var(--white);
    border-radius: 8px;
    box-shadow: 0 2px 4px var(--shadow-color);
    margin-bottom: 20px;
    overflow: hidden;
}

.card-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h2 {
    font-size: 16px;
    font-weight: 600;
}

.card-body {
    padding: 20px;
}

/* Profile Card */
.profile-card {
    display: flex;
    align-items: center;
    padding: 20px;
}

.profile-image {
    width: 100px;
    height: 100px;
    border-radius: 8px;
    object-fit: cover;
    margin-right: 20px;
}

.profile-details {
    flex: 1;
}

.profile-name {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 5px;
}

.profile-specialty {
    font-size: 16px;
    color: var(--text-light);
    margin-bottom: 10px;
}

.profile-info {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 15px;
}

.profile-info-item {
    display: flex;
    align-items: center;
    margin-right: 20px;
    margin-bottom: 5px;
}

.profile-info-item i {
    margin-right: 5px;
    color: var(--primary-color);
}

.profile-actions {
    display: flex;
    gap: 10px;
}

.btn {
    padding: 8px 15px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-light);
}

.btn-danger {
    background-color: var(--danger-color);
    color: var(--white);
}

.btn-danger:hover {
    background-color: #c0392b;
}

/* Stats Cards */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background-color: var(--white);
    border-radius: 8px;
    box-shadow: 0 2px 4px var(--shadow-color);
    padding: 20px;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px var(--shadow-color);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 20px;
    color: var(--white);
}

.stat-icon.primary {
    background-color: var(--primary-color);
}

.stat-icon.success {
    background-color: var(--success-color);
}

.stat-icon.warning {
    background-color: var(--warning-color);
}

.stat-icon.info {
    background-color: var(--info-color);
}

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 5px;
}

.stat-label {
    color: var(--text-light);
    font-size: 14px;
}

/* Notification */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 4px;
    color: var(--white);
    font-weight: 500;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    transform: translateX(110%);
    transition: transform 0.3s ease;
    z-index: 1000;
    max-width: 300px;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background-color: var(--success-color);
}

.notification.error {
    background-color: var(--danger-color);
}

.notification.warning {
    background-color: var(--warning-color);
}

.notification.info {
    background-color: var(--info-color);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    overflow: auto;
}

.modal-content {
    background-color: var(--white);
    margin: 10% auto;
    padding: 0;
    width: 50%;
    max-width: 600px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    animation: modalFadeIn 0.3s;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.modal-close {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-light);
    cursor: pointer;
}

.modal-close:hover {
    color: var(--danger-color);
}

.modal-body {
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid var(--border-color);
    text-align: right;
}

/* User Dropdown */
.user-dropdown {
    position: absolute;
    background-color: var(--white);
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    min-width: 200px;
    z-index: 100;
    display: none;
}

.user-dropdown.show {
    display: block;
}

.user-dropdown ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.user-dropdown ul li {
    padding: 0;
    margin: 0;
}

.user-dropdown ul li a {
    display: block;
    padding: 10px 15px;
    color: var(--text-dark);
    transition: all 0.2s ease;
}

.user-dropdown ul li a:hover {
    background-color: var(--gray-100);
    color: var(--primary-color);
}

.user-dropdown ul li a i {
    margin-right: 10px;
    color: var(--primary-color);
}

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 14px;
    color: var(--text-light);
}

/* Table */
.table-container {
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
}

table th, table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

table th {
    font-weight: 600;
    color: var(--text-light);
    background-color: var(--gray-100);
}

table tr:hover {
    background-color: var(--gray-100);
}

.table-actions {
    display: flex;
    gap: 5px;
}

.action-btn {
    width: 30px;
    height: 30px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-btn.view {
    background-color: var(--info-color);
}

.action-btn.edit {
    background-color: var(--warning-color);
}

.action-btn.delete {
    background-color: var(--danger-color);
}

.action-btn:hover {
    opacity: 0.8;
}

/* Status Badges */
.status {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.status.pending {
    background-color: var(--warning-color);
    color: var(--white);
}

.status.confirmed {
    background-color: var(--success-color);
    color: var(--white);
}

.status.cancelled {
    background-color: var(--danger-color);
    color: var(--white);
}

.status.completed {
    background-color: var(--info-color);
    color: var(--white);
}

/* Responsive */
@media (max-width: 992px) {
    .sidebar {
        position: fixed;
        left: -250px;
        height: 100%;
    }

    .sidebar.active {
        left: 0;
    }

    .main-content {
        margin-left: 0;
    }

    .stats-container {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .top-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .header-nav {
        margin: 10px 0;
        width: 100%;
        overflow-x: auto;
        padding-bottom: 5px;
    }

    .header-actions {
        width: 100%;
        justify-content: space-between;
    }

    .search-container {
        flex: 1;
        max-width: 200px;
    }

    .stats-container {
        grid-template-columns: 1fr;
    }

    .profile-card {
        flex-direction: column;
        text-align: center;
    }

    .profile-image {
        margin-right: 0;
        margin-bottom: 15px;
    }

    .profile-info {
        justify-content: center;
    }
}
