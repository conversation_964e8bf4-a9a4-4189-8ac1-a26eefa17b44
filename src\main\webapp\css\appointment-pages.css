/* Appointment Pages Styles - Updated Version */

/* Common Styles */
.appointment-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* Appointment Confirmation Page */
.confirmation-header {
    background-color: #4CAF50;
    color: white;
    padding: 15px 20px;
    border-radius: 6px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.confirmation-header i {
    font-size: 24px;
    margin-right: 10px;
}

.confirmation-header h2 {
    margin: 0;
    font-size: 1.5rem;
}

.appointment-details {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 20px;
}

.detail-row {
    display: flex;
    margin-bottom: 15px;
    border-bottom: 1px solid #eee;
    padding-bottom: 15px;
}

.detail-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.detail-label {
    width: 150px;
    font-weight: 600;
    color: #555;
}

.detail-value {
    flex: 1;
    color: #333;
}

.doctor-info {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.doctor-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    overflow: hidden;
}

.doctor-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.doctor-avatar .initials {
    font-size: 24px;
    font-weight: 600;
    color: white;
}

.doctor-details {
    flex: 1;
}

.doctor-name {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.doctor-specialty {
    color: #666;
    font-size: 0.9rem;
}

.appointment-instructions {
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
    padding: 15px 20px;
    margin-bottom: 20px;
    border-radius: 4px;
}

.appointment-instructions h3 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #856404;
    font-size: 1.1rem;
}

.appointment-instructions ul {
    margin: 0;
    padding-left: 20px;
}

.appointment-instructions li {
    margin-bottom: 8px;
    color: #856404;
}

.appointment-instructions li:last-child {
    margin-bottom: 0;
}

.action-buttons {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    text-decoration: none;
    text-align: center;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #1976d2;
    color: white;
}

.btn-primary:hover {
    background-color: #1565c0;
}

.btn-secondary {
    background-color: #4CAF50;
    color: white;
}

.btn-secondary:hover {
    background-color: #43a047;
}

.btn-outline {
    background-color: transparent;
    border: 1px solid #1976d2;
    color: #1976d2;
}

.btn-outline:hover {
    background-color: #f0f7ff;
}

/* Patient Appointment List */
.appointment-list {
    margin-top: 20px;
}

.appointment-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    margin-bottom: 15px;
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.appointment-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.appointment-card-header {
    padding: 15px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.appointment-date {
    font-weight: 600;
    color: #333;
}

.appointment-status {
    display: inline-block;
    padding: 4px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-pending {
    background-color: rgba(255, 152, 0, 0.1);
    color: #FF9800;
    border: 1px solid rgba(255, 152, 0, 0.3);
}

.status-confirmed {
    background-color: rgba(76, 175, 80, 0.1);
    color: #4CAF50;
    border: 1px solid rgba(76, 175, 80, 0.3);
}

.status-cancelled {
    background-color: rgba(244, 67, 54, 0.1);
    color: #F44336;
    border: 1px solid rgba(244, 67, 54, 0.3);
}

.status-completed {
    background-color: rgba(33, 150, 243, 0.1);
    color: #2196F3;
    border: 1px solid rgba(33, 150, 243, 0.3);
}

.appointment-card-body {
    padding: 15px;
}

.appointment-doctor {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.doctor-small-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    overflow: hidden;
}

.doctor-small-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.doctor-small-avatar .initials {
    font-size: 16px;
    font-weight: 600;
    color: white;
}

.appointment-doctor-info {
    flex: 1;
}

.appointment-doctor-name {
    font-weight: 600;
    margin-bottom: 2px;
}

.appointment-doctor-specialty {
    font-size: 0.85rem;
    color: #666;
}

.appointment-time {
    margin-bottom: 10px;
    color: #555;
    font-size: 0.9rem;
}

.appointment-time i {
    margin-right: 5px;
    color: #777;
}

.appointment-card-footer {
    padding: 10px 15px;
    background-color: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.appointment-actions {
    display: flex;
    gap: 10px;
}

.btn-sm {
    padding: 5px 10px;
    font-size: 0.85rem;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    overflow: auto;
}

.modal-content {
    background-color: #fff;
    margin: 15% auto;
    padding: 0;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    width: 400px;
    max-width: 90%;
    animation: modalFadeIn 0.3s;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.2rem;
}

.close {
    color: #aaa;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.2s;
}

.close:hover {
    color: #333;
}

.modal-body {
    padding: 20px;
}

.modal-body p {
    margin: 0 0 10px 0;
    color: #555;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .detail-row {
        flex-direction: column;
    }

    .detail-label {
        width: 100%;
        margin-bottom: 5px;
    }

    .action-buttons {
        flex-direction: column;
    }

    .btn {
        width: 100%;
    }

    .modal-content {
        margin: 30% auto;
    }
}
