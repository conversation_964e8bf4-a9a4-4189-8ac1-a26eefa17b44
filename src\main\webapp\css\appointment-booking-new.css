/* Appointment Booking Styles */
.main-content {
    margin-left: 220px;
    padding: 30px;
    background-color: #f5f7fa;
    min-height: 100vh;
}

.booking-header {
    background-color: #4CAF50;
    color: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 25px;
    display: flex;
    align-items: center;
}

.booking-header h2 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
}

.doctor-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    padding: 20px;
    margin-bottom: 25px;
    display: flex;
    align-items: center;
}

.doctor-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    font-size: 30px;
    font-weight: 600;
    color: #4CAF50;
}

.doctor-info {
    flex: 1;
}

.doctor-name {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin: 0 0 5px 0;
}

.doctor-specialty {
    color: #666;
    margin: 0 0 10px 0;
}

.doctor-details {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 10px;
}

.doctor-detail {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #555;
}

.doctor-detail i {
    margin-right: 5px;
    color: #4CAF50;
}

.booking-form {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    padding: 25px;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 16px;
}

.form-control:focus {
    border-color: #4CAF50;
    outline: none;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
}

.time-slots {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 10px;
}

.time-slot {
    position: relative;
}

.time-slot input[type="radio"] {
    position: absolute;
    opacity: 0;
}

.time-slot label {
    display: block;
    padding: 12px 10px;
    text-align: center;
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.time-slot input[type="radio"]:checked + label {
    background-color: #4CAF50;
    color: white;
    border-color: #4CAF50;
}

.time-slot.unavailable label {
    background-color: #f8d7da;
    color: #721c24;
    text-decoration: line-through;
    cursor: not-allowed;
}

.symptoms-textarea {
    width: 100%;
    min-height: 120px;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 6px;
    resize: vertical;
    font-family: inherit;
    font-size: 16px;
}

.symptoms-textarea:focus {
    border-color: #4CAF50;
    outline: none;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
}

.btn {
    display: inline-block;
    padding: 12px 20px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    text-align: center;
    text-decoration: none;
    transition: all 0.3s ease;
}

.btn:hover {
    background-color: #388E3C;
}

.btn-primary {
    background-color: #4CAF50;
    color: white;
}

.btn-secondary {
    background-color: #f8f9fa;
    color: #333;
    border: 1px solid #ddd;
}

.btn-secondary:hover {
    background-color: #e9ecef;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 20px;
}

.alert {
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.form-error {
    color: #dc3545;
    font-size: 14px;
    margin-top: 5px;
}

/* Responsive styles */
@media (max-width: 992px) {
    .main-content {
        margin-left: 80px;
    }
}

@media (max-width: 768px) {
    .main-content {
        margin-left: 0;
        padding: 20px;
    }
    
    .doctor-card {
        flex-direction: column;
        text-align: center;
    }
    
    .doctor-avatar {
        margin: 0 auto 15px;
    }
    
    .doctor-details {
        justify-content: center;
    }
    
    .time-slots {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
    }
}
