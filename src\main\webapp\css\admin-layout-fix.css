/*
 * Admin Layout Fix - Specific CSS for Admin Interface Only
 * This CSS file fixes the admin sidebar and layout issues
 * Uses very specific selectors to avoid affecting other interfaces
 */

/* Admin-specific body class to target only admin pages */
.admin-interface {
    margin: 0;
    padding: 0;
    font-family: 'Poppins', sans-serif;
}

/* Admin sidebar - only applies when admin-interface class is present */
.admin-interface .sidebar {
    width: 250px !important;
    height: 100vh !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    z-index: 1000 !important;
    background-color: #fff !important;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1) !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    transition: all 0.3s ease !important;
    padding: 0 !important;
    margin: 0 !important;
    border-right: 1px solid #e0e0e0 !important;
    float: none !important;
    display: flex !important;
    flex-direction: column !important;
}

/* Admin sidebar header */
.admin-interface .sidebar .sidebar-header {
    padding: 15px !important;
    border-bottom: 1px solid #e0e0e0 !important;
    background-color: #fff !important;
    display: flex !important;
    align-items: center !important;
    flex-shrink: 0 !important;
}

.admin-interface .sidebar .sidebar-header h3 {
    margin: 0 !important;
    font-size: 1.2rem !important;
    font-weight: 600 !important;
    color: #333 !important;
    flex: 1 !important;
}

.admin-interface .sidebar .sidebar-header img {
    height: 40px !important;
    width: auto !important;
    margin-right: 10px !important;
}

/* Admin profile info */
.admin-interface .sidebar .profile-info {
    margin-top: 10px !important;
    text-align: center !important;
}

.admin-interface .sidebar .profile-image {
    width: 60px !important;
    height: 60px !important;
    border-radius: 50% !important;
    overflow: hidden !important;
    margin: 0 auto 10px auto !important;
    background-color: #f0f0f0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.admin-interface .sidebar .profile-image img {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
}

.admin-interface .sidebar .user-name {
    font-size: 0.9rem !important;
    font-weight: 500 !important;
    color: #333 !important;
    margin-bottom: 2px !important;
}

.admin-interface .sidebar .user-role {
    font-size: 0.8rem !important;
    color: #666 !important;
}

/* Admin sidebar menu */
.admin-interface .sidebar .sidebar-menu {
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
    flex: 1 !important;
    overflow-y: auto !important;
}

.admin-interface .sidebar .menu-item {
    margin: 0 !important;
    border-bottom: 1px solid #f0f0f0 !important;
}

.admin-interface .sidebar .menu-link {
    display: flex !important;
    align-items: center !important;
    padding: 12px 20px !important;
    color: #333 !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
    border-left: 3px solid transparent !important;
}

.admin-interface .sidebar .menu-link i {
    margin-right: 10px !important;
    width: 20px !important;
    text-align: center !important;
    font-size: 16px !important;
}

.admin-interface .sidebar .menu-link span {
    font-size: 14px !important;
    font-weight: 400 !important;
}

/* Admin badge styling */
.admin-interface .sidebar .badge {
    background-color: #dc3545 !important;
    color: white !important;
    font-size: 10px !important;
    padding: 2px 6px !important;
    border-radius: 10px !important;
    margin-left: auto !important;
}

/* Admin menu link hover state */
.admin-interface .sidebar .menu-link:hover {
    background-color: #f5f5f5 !important;
    color: #4CAF50 !important;
    text-decoration: none !important;
}

/* Admin active menu item */
.admin-interface .sidebar .menu-item.active .menu-link {
    background-color: #f5f5f5 !important;
    color: #4CAF50 !important;
    border-left-color: #4CAF50 !important;
    font-weight: 500 !important;
}

/* Admin main content positioning - no shifting */
.admin-interface .main-content,
.admin-interface .dashboard-content {
    margin-left: 0 !important;
    padding: 20px !important;
    padding-left: 270px !important; /* Add left padding to account for sidebar */
    transition: none !important;
    width: 100% !important;
    float: none !important;
    clear: none !important;
    position: relative !important;
    min-height: 100vh !important;
    background-color: #f8f9fc !important;
    box-sizing: border-box !important;
    display: block !important;
}

/* Admin dashboard-main layout - no shifting */
.admin-interface .dashboard-main {
    margin-left: 0 !important;
    width: 100% !important;
    padding: 0 !important;
    padding-left: 250px !important; /* Add left padding to account for sidebar */
    background-color: #f8f9fc !important;
    min-height: 100vh !important;
    box-sizing: border-box !important;
}

.admin-interface .dashboard-content {
    padding: 20px !important;
    width: 100% !important;
    box-sizing: border-box !important;
}

.admin-interface .dashboard-nav {
    padding: 15px 20px !important;
    background-color: #fff !important;
    border-bottom: 1px solid #e0e0e0 !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
}

/* Admin page header */
.admin-interface .main-content .page-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 20px !important;
    padding-bottom: 10px !important;
    border-bottom: 1px solid #e0e0e0 !important;
}

.admin-interface .main-content .header-left {
    display: flex !important;
    align-items: center !important;
    gap: 15px !important;
}

.admin-interface .main-content .menu-toggle {
    display: none !important;
    background: none !important;
    border: none !important;
    font-size: 18px !important;
    color: #333 !important;
    cursor: pointer !important;
    padding: 5px !important;
    border-radius: 4px !important;
}

.admin-interface .main-content .menu-toggle:hover {
    background-color: #f5f5f5 !important;
}

/* Admin dashboard container */
.admin-interface .dashboard-container {
    display: flex !important;
    min-height: 100vh !important;
    width: 100% !important;
    position: relative !important;
}

/* Responsive styles for admin interface */
@media (max-width: 992px) {
    .admin-interface .sidebar {
        width: 70px !important;
    }

    .admin-interface .sidebar .sidebar-header h3,
    .admin-interface .sidebar .profile-info,
    .admin-interface .sidebar .menu-link span {
        display: none !important;
    }

    .admin-interface .sidebar .menu-link {
        justify-content: center !important;
        padding: 15px 0 !important;
    }

    .admin-interface .sidebar .menu-link i {
        margin-right: 0 !important;
        font-size: 18px !important;
    }

    .admin-interface .main-content,
    .admin-interface .dashboard-content {
        padding-left: 90px !important; /* Adjust padding for collapsed sidebar */
    }

    .admin-interface .dashboard-main {
        padding-left: 70px !important; /* Adjust padding for collapsed sidebar */
    }

    .admin-interface .main-content .menu-toggle {
        display: block !important;
    }
}

@media (max-width: 768px) {
    .admin-interface .sidebar {
        width: 0 !important;
        overflow: hidden !important;
        transform: translateX(-100%) !important;
    }

    .admin-interface .sidebar.active {
        width: 250px !important;
        transform: translateX(0) !important;
    }

    .admin-interface .sidebar.active .sidebar-header h3,
    .admin-interface .sidebar.active .profile-info,
    .admin-interface .sidebar.active .menu-link span {
        display: block !important;
    }

    .admin-interface .sidebar.active .menu-link {
        justify-content: flex-start !important;
        padding: 12px 20px !important;
    }

    .admin-interface .sidebar.active .menu-link i {
        margin-right: 10px !important;
    }

    .admin-interface .main-content,
    .admin-interface .dashboard-content {
        padding-left: 20px !important; /* Reset to normal padding on mobile */
        padding: 15px !important;
    }

    .admin-interface .dashboard-main {
        padding-left: 0 !important; /* Reset padding on mobile */
    }

    .admin-interface .main-content .menu-toggle {
        display: block !important;
    }

    .admin-interface .main-content .page-header {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 10px !important;
    }
}
