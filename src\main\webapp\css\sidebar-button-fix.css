/* CSS fixes for My Appointments and My Patients buttons in the sidebar */

/* We're not modifying the layout here, just the button styles */

/* Common styles for both doctor and patient sidebars */
.sidebar-menu li a,
.sidebar-nav a {
    display: flex !important;
    align-items: center !important;
    width: 100% !important;
    padding: 12px 20px !important;
    color: #555 !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
    font-size: 14px !important;
    border-left: 3px solid transparent !important;
}

.sidebar-menu li a i,
.sidebar-nav a i {
    margin-right: 10px !important;
    width: 20px !important;
    text-align: center !important;
    font-size: 16px !important;
}

.sidebar-menu li a:hover,
.sidebar-nav a:hover {
    background-color: #f5f5f5 !important;
    color: #4CAF50 !important;
}

.sidebar-menu li.active a,
.sidebar-nav a.active {
    background-color: #f5f5f5 !important;
    color: #4CAF50 !important;
    border-left: 3px solid #4CAF50 !important;
    font-weight: 500 !important;
}

/* Specific fixes for doctor sidebar */
.sidebar-nav li {
    margin-bottom: 8px !important;
    width: 100% !important;
    list-style: none !important;
}

/* Specific fixes for patient sidebar */
.sidebar-menu li {
    margin-bottom: 5px !important;
    width: 100% !important;
    list-style: none !important;
}

/* Responsive styles */
@media (max-width: 992px) {
    .sidebar-menu li a span {
        display: inline-block !important;
    }
}

@media (max-width: 768px) {
    .sidebar-menu li a,
    .sidebar-nav a {
        padding: 12px 15px !important;
    }
}
