/* Doctor Profile Image Styles */

/* Sidebar Profile Image */
.profile-image {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto 15px;
    border: 3px solid white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.profile-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-image .profile-initials {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #4CAF50;
    color: white;
    font-size: 1.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

/* Profile Edit Page Image */
.profile-image-container {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto 15px;
    border: 3px solid white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
    background-color: #f0f0f0;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.profile-image-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-image-container .profile-initials {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #4CAF50;
    color: white;
    font-size: 2.2rem;
    font-weight: 600;
    text-transform: uppercase;
}

/* Header Profile Image */
.user-profile-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid #f0f0f0;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-profile-icon img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-profile-icon .profile-initials {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #4CAF50;
    color: white;
    font-size: 1rem;
    font-weight: 600;
    text-transform: uppercase;
}

/* Error Message Styles */
.error-message {
    background-color: #ffebee;
    color: #c62828;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 8px;
    border: 1px solid #ffcdd2;
    display: flex;
    align-items: center;
    font-weight: 500;
}

.error-message i {
    margin-right: 10px;
    font-size: 18px;
}

.success-message {
    background-color: #e8f5e9;
    color: #2e7d32;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 8px;
    border: 1px solid #c8e6c9;
    display: flex;
    align-items: center;
    font-weight: 500;
}

.success-message i {
    margin-right: 10px;
    font-size: 18px;
}

/* Form Feedback Styles */
.form-feedback {
    padding: 15px;
    margin: 20px 0;
    border-radius: 8px;
    text-align: center;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.form-feedback i {
    margin-right: 10px;
    font-size: 18px;
}

.form-feedback.success {
    background-color: #e8f5e9;
    color: #2e7d32;
    border: 1px solid #c8e6c9;
}

.form-feedback.error {
    background-color: #ffebee;
    color: #c62828;
    border: 1px solid #ffcdd2;
}
