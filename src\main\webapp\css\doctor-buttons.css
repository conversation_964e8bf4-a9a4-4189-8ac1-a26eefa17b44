/* Doctor <PERSON> Styles */
:root {
    --primary-color: #3CCFCF;
    --primary-dark: #2AACAC;
    --primary-light: #B3EDED;
    --secondary-color: #2ecc71;
    --secondary-dark: #27ae60;
    --secondary-light: #a9f5bc;
    --accent-color: #e74c3c;
    --dark-color: #2c3e50;
    --light-color: #f9f9f9;
    --danger-color: #e74c3c;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --info-color: #3498db;
    --text-dark: #2c3e50;
    --text-light: #7f8c8d;
    --border-color: #e0e0e0;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --white: #ffffff;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;
}

/* Standard Button */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    text-decoration: none;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.btn i {
    margin-right: 8px;
}

/* Button Variants */
.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-secondary {
    background-color: var(--gray-600);
    color: white;
}

.btn-secondary:hover {
    background-color: var(--gray-700);
}

.btn-success {
    background-color: var(--success-color);
    color: white;
}

.btn-success:hover {
    background-color: var(--secondary-dark);
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background-color: #c0392b;
}

.btn-warning {
    background-color: var(--warning-color);
    color: white;
}

.btn-warning:hover {
    background-color: #d35400;
}

.btn-info {
    background-color: var(--info-color);
    color: white;
}

.btn-info:hover {
    background-color: #2980b9;
}

/* Button Sizes */
.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.btn-lg {
    padding: 12px 24px;
    font-size: 16px;
}

/* Outline Buttons */
.btn-outline-primary {
    background-color: transparent;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-light);
}

.btn-outline-secondary {
    background-color: transparent;
    border: 1px solid var(--gray-600);
    color: var(--gray-600);
}

.btn-outline-secondary:hover {
    background-color: var(--gray-200);
}

/* Action Buttons */
.action-btn {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.action-btn.view {
    background-color: var(--info-color);
}

.action-btn.edit {
    background-color: var(--warning-color);
}

.action-btn.delete {
    background-color: var(--danger-color);
}

/* Button Groups */
.btn-group {
    display: flex;
    gap: 10px;
}

.btn-group-vertical {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

/* Disabled Button */
.btn:disabled, .btn.disabled {
    opacity: 0.65;
    cursor: not-allowed;
    pointer-events: none;
}

/* Loading Button */
.btn.loading {
    position: relative;
    color: transparent !important;
}

.btn.loading::after {
    content: "";
    position: absolute;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}
