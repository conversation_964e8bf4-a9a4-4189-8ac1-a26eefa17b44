/* Appointment Confirmation Fix Styles */

/* Main container */
.dashboard-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar styles */
.sidebar {
    width: 250px;
    position: fixed;
    left: 0;
    top: 0;
    height: 100%;
    background-color: #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    overflow-y: auto;
}

.user-profile {
    padding: 30px 20px;
    text-align: center;
    border-bottom: 1px solid #eee;
}

.profile-image {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto 15px;
    border: 5px solid #f0f0f0;
}

.profile-initials {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #4CAF50;
    color: white;
    font-size: 2rem;
    font-weight: 600;
}

.user-name {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 5px;
    color: #333;
}

.user-role {
    font-size: 0.9rem;
    color: #666;
}

/* Main content */
.dashboard-main {
    flex: 1;
    margin-left: 250px;
    padding: 20px;
}

/* Confirmation styles */
.appointment-container {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 30px;
}

.confirmation-header {
    background: linear-gradient(135deg, #4CAF50, #8BC34A);
    color: white;
    padding: 30px;
    text-align: center;
}

.confirmation-header i {
    font-size: 48px;
    margin-bottom: 15px;
    display: block;
}

.confirmation-header h2 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
}

/* Doctor info */
.doctor-info {
    display: flex;
    align-items: center;
    padding: 20px;
    background-color: #f1f8e9;
    border-bottom: 1px solid #e0e0e0;
}

.doctor-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 20px;
    background-color: #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.doctor-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.doctor-avatar .initials {
    font-size: 32px;
    font-weight: 600;
    color: #4CAF50;
}

.doctor-details {
    flex: 1;
}

.doctor-name {
    margin: 0 0 5px 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.doctor-specialty {
    margin: 0;
    color: #666;
    font-size: 14px;
}

/* Appointment details */
.appointment-details {
    padding: 20px;
    border-bottom: 1px solid #e0e0e0;
}

.detail-row {
    display: flex;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;
}

.detail-row:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.detail-label {
    width: 150px;
    font-weight: 600;
    color: #555;
}

.detail-value {
    flex: 1;
    color: #333;
}

.status-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-confirmed {
    background-color: #d4edda;
    color: #155724;
}

.status-cancelled {
    background-color: #f8d7da;
    color: #721c24;
}

/* Instructions */
.appointment-instructions {
    padding: 20px;
    background-color: #fff3cd;
    border-bottom: 1px solid #e0e0e0;
}

.appointment-instructions h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 18px;
    color: #856404;
}

.appointment-instructions ul {
    margin: 0;
    padding-left: 20px;
}

.appointment-instructions li {
    margin-bottom: 10px;
    color: #856404;
}

/* Action buttons */
.action-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    padding: 20px;
}

.btn {
    padding: 12px 20px;
    border-radius: 5px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s;
}

.btn i {
    margin-right: 8px;
}

.btn-primary {
    background-color: #4CAF50;
    color: white;
    border: none;
}

.btn-primary:hover {
    background-color: #388E3C;
}

.btn-secondary {
    background-color: #f5f5f5;
    color: #333;
    border: 1px solid #ddd;
}

.btn-secondary:hover {
    background-color: #e0e0e0;
}

/* Responsive styles */
@media (max-width: 768px) {
    .dashboard-container {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        position: relative;
        height: auto;
    }
    
    .dashboard-main {
        margin-left: 0;
    }
    
    .detail-row {
        flex-direction: column;
    }
    
    .detail-label {
        width: 100%;
        margin-bottom: 5px;
    }
    
    .doctor-info {
        flex-direction: column;
        text-align: center;
    }
    
    .doctor-avatar {
        margin: 0 auto 15px;
    }
    
    .doctor-details {
        text-align: center;
    }
    
    .action-buttons {
        flex-direction: column;
    }
}
