/* Fix for appointment booking page */

/* Override the sidebar styles for the booking page */
.dashboard-container {
    display: block;
    min-height: auto;
}

.sidebar {
    width: 250px;
    position: fixed;
    left: 0;
    top: 0;
    height: 100%;
    background-color: #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    overflow-y: auto;
}

.main-content {
    margin-left: 250px;
    padding: 20px;
    transition: margin-left 0.3s;
}

/* Card styles */
.card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 30px;
}

.card-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    background-color: #f8f9fa;
}

.card-header h2 {
    margin: 0;
    font-size: 1.5rem;
    color: #333;
    font-weight: 600;
}

.card-body {
    padding: 25px;
}

/* Doctor card styles */
.doctor-card {
    display: flex;
    align-items: center;
    padding: 25px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    margin-bottom: 25px;
    border: 1px solid #e0e0e0;
}

.doctor-image {
    width: 110px;
    height: 110px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 25px;
    flex-shrink: 0;
    border: 4px solid #fff;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.doctor-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.doctor-info {
    flex: 1;
}

.doctor-info h4 {
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 12px;
    color: #2c3e50;
}

.doctor-info p {
    margin: 10px 0;
    font-size: 1rem;
    color: #555;
    display: flex;
    align-items: center;
    line-height: 1.5;
}

.doctor-info p i {
    margin-right: 12px;
    color: #4CAF50;
    width: 18px;
    text-align: center;
    font-size: 1.1rem;
}

.fee-text {
    font-weight: 600;
    color: #4CAF50;
    font-size: 1.2rem;
    margin-top: 15px;
    padding: 8px 15px;
    background-color: #f1f8e9;
    border-radius: 4px;
    display: inline-block;
}

/* Booking form container */
.booking-form-container {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
}

/* Form styles */
.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 10px;
    font-weight: 500;
    color: #2c3e50;
    font-size: 1.1rem;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 1rem;
    transition: all 0.3s;
}

.form-control:focus {
    border-color: #4CAF50;
    outline: none;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
}

/* Time slots styles */
.time-slots-title {
    font-size: 1.1rem;
    font-weight: 500;
    margin-bottom: 10px;
    color: #2c3e50;
}

.time-slots {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(110px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.time-slot {
    position: relative;
}

.time-slot input[type="radio"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.time-slot label {
    display: block;
    padding: 14px 10px;
    text-align: center;
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
    font-size: 1rem;
    color: #555;
}

.time-slot input[type="radio"]:checked + label {
    background-color: #4CAF50;
    color: white;
    border-color: #4CAF50;
    box-shadow: 0 3px 8px rgba(76, 175, 80, 0.3);
    transform: translateY(-2px);
}

.time-slot label:hover {
    background-color: #e9ecef;
    border-color: #ced4da;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.time-slot input[type="radio"]:checked + label:hover {
    background-color: #43A047;
}

/* Reason/Symptoms section */
.reason-container {
    margin-top: 25px;
}

.reason-title {
    font-size: 1.1rem;
    margin-bottom: 10px;
    color: #2c3e50;
    font-weight: 500;
}

.reason-input {
    min-height: 120px;
    resize: vertical;
    padding: 15px;
    font-size: 0.95rem;
}

/* Action buttons */
.action-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 35px;
    gap: 20px;
}

.btn {
    padding: 12px 25px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
    border: none;
    font-size: 1rem;
    text-decoration: none;
}

.btn i {
    margin-right: 8px;
}

.btn-primary {
    background-color: #4CAF50;
    color: white;
    box-shadow: 0 2px 5px rgba(76, 175, 80, 0.3);
}

.btn-primary:hover {
    background-color: #43A047;
    box-shadow: 0 4px 8px rgba(76, 175, 80, 0.4);
    transform: translateY(-2px);
}

.btn-secondary {
    background-color: #f8f9fa;
    color: #555;
    border: 1px solid #ddd;
}

.btn-secondary:hover {
    background-color: #e9ecef;
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Date input styling */
input[type="date"] {
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 1rem;
    width: 100%;
    color: #333;
    background-color: #fff;
}

input[type="date"]:focus {
    border-color: #4CAF50;
    outline: none;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
}

/* Responsive styles */
@media (max-width: 768px) {
    .doctor-card {
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding: 20px 15px;
    }
    
    .doctor-image {
        margin-right: 0;
        margin-bottom: 20px;
    }
    
    .doctor-info p {
        justify-content: center;
    }
    
    .time-slots {
        grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
        gap: 10px;
    }
    
    .action-buttons {
        flex-direction: column-reverse;
        gap: 15px;
    }
    
    .action-buttons .btn {
        width: 100%;
    }
    
    .booking-form-container {
        padding: 20px 15px;
    }
    
    .fee-text {
        display: block;
        width: 100%;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .time-slots {
        grid-template-columns: repeat(3, 1fr);
        gap: 8px;
    }
    
    .time-slot label {
        padding: 10px 8px;
        font-size: 0.9rem;
    }
    
    .doctor-info h4 {
        font-size: 1.2rem;
    }
    
    .doctor-image {
        width: 90px;
        height: 90px;
    }
}
